{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.13.0", "@repo/ui": "workspace:*", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "lucide-react": "^0.536.0", "next": "^15.4.2", "next-auth": "^4.24.11", "openai": "^5.12.0", "postcss": "^8.5.6", "prisma": "^6.13.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.62.0", "tailwindcss": "^4.1.11", "zod": "^4.0.15"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.31.0", "typescript": "5.8.2"}}