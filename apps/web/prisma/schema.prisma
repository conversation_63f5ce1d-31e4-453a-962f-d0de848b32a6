// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // Subscription info
  subscriptionTier String @default("FREE") // FREE, STARTER, GROWTH, PRO
  subscriptionStatus String @default("ACTIVE") // ACTIVE, CANCELLED, EXPIRED
  subscriptionEndsAt DateTime?
  
  accounts Account[]
  sessions Session[]
  stores   Store[]
  aiCredits AICredit[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Store {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  niche       String
  status      String   @default("DRAFT") // DRAFT, ACTIVE, PAUSED, ARCHIVED
  platform    String   // SHOPIFY, NEXUS_COMMERCE
  
  // Store URLs and identifiers
  shopifyStoreId String?
  customDomain   String?
  subdomain      String? // for nexus commerce
  
  // Brand identity
  logo        String?
  colorPrimary String?
  colorSecondary String?
  fontFamily  String?
  brandVoice  String?
  missionStatement String?
  
  // Analytics
  totalRevenue Decimal @default(0)
  totalOrders  Int     @default(0)
  conversionRate Decimal @default(0)
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  products     Product[]
  orders       Order[]
  analytics    Analytics[]
  aiContent    AIContent[]
  nicheAnalysis NicheAnalysis[]
  competitorAnalysis CompetitorAnalysis[]
}

model NicheAnalysis {
  id          String   @id @default(cuid())
  niche       String
  marketSize  String
  competition String   // LOW, MEDIUM, HIGH
  profitability String // LOW, MEDIUM, HIGH
  trendData   Json?
  nicheScore  Decimal
  analysis    String   @db.Text
  
  createdAt DateTime @default(now())
  
  storeId String
  store   Store  @relation(fields: [storeId], references: [id], onDelete: Cascade)
}

model CompetitorAnalysis {
  id           String @id @default(cuid())
  competitorName String
  competitorUrl String
  strengths    String @db.Text
  weaknesses   String @db.Text
  opportunities String @db.Text
  threats      String @db.Text
  
  createdAt DateTime @default(now())
  
  storeId String
  store   Store  @relation(fields: [storeId], references: [id], onDelete: Cascade)
}

model Product {
  id          String  @id @default(cuid())
  name        String
  description String  @db.Text
  price       Decimal
  compareAtPrice Decimal?
  sku         String?
  
  // Supplier info
  supplierName String
  supplierProductId String
  supplierPrice Decimal
  shippingTime String?
  
  // SEO and content
  seoTitle    String?
  seoDescription String?
  tags        String[]
  
  // Product status
  status      String @default("DRAFT") // DRAFT, ACTIVE, ARCHIVED
  isWinning   Boolean @default(false)
  
  // Analytics
  views       Int @default(0)
  orders      Int @default(0)
  revenue     Decimal @default(0)
  
  images      String[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  storeId String
  store   Store  @relation(fields: [storeId], references: [id], onDelete: Cascade)
  
  orderItems OrderItem[]
}

model Order {
  id          String   @id @default(cuid())
  orderNumber String   @unique
  email       String
  total       Decimal
  subtotal    Decimal
  tax         Decimal  @default(0)
  shipping    Decimal  @default(0)
  status      String   @default("PENDING") // PENDING, PAID, SHIPPED, DELIVERED, CANCELLED
  
  // Customer info
  customerName String
  shippingAddress Json
  billingAddress Json?
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  storeId String
  store   Store  @relation(fields: [storeId], references: [id], onDelete: Cascade)
  
  items OrderItem[]
}

model OrderItem {
  id       String  @id @default(cuid())
  quantity Int
  price    Decimal
  
  orderId   String
  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
}

model Analytics {
  id          String   @id @default(cuid())
  date        DateTime
  visitors    Int      @default(0)
  pageViews   Int      @default(0)
  orders      Int      @default(0)
  revenue     Decimal  @default(0)
  conversionRate Decimal @default(0)
  
  // Traffic sources
  organicTraffic Int @default(0)
  paidTraffic    Int @default(0)
  socialTraffic  Int @default(0)
  directTraffic  Int @default(0)
  
  storeId String
  store   Store  @relation(fields: [storeId], references: [id], onDelete: Cascade)
  
  @@unique([storeId, date])
}

model AIContent {
  id          String   @id @default(cuid())
  type        String   // PRODUCT_DESCRIPTION, BLOG_POST, EMAIL, AD_COPY, SOCIAL_MEDIA
  title       String?
  content     String   @db.Text
  metadata    Json?    // Additional data like target audience, tone, etc.
  
  createdAt DateTime @default(now())
  
  storeId String
  store   Store  @relation(fields: [storeId], references: [id], onDelete: Cascade)
}

model AICredit {
  id          String   @id @default(cuid())
  type        String   // CONTENT_GENERATION, IMAGE_GENERATION, ANALYSIS
  amount      Int      // Credits used
  description String?
  
  createdAt DateTime @default(now())
  
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
}
