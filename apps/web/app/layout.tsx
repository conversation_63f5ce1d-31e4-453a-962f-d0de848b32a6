import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Juaso AI Commerce - Build Smarter E-commerce Stores",
  description: "The world's most intelligent AI-powered platform for launching, managing, and growing successful e-commerce businesses. Go beyond simple store generation with strategic AI guidance.",
  keywords: "AI e-commerce, dropshipping, store builder, Shopify automation, AI marketing, online business",
  authors: [{ name: "Juaso AI Commerce" }],
  openGraph: {
    title: "Juaso AI Commerce - Build Smarter E-commerce Stores",
    description: "The world's most intelligent AI-powered platform for launching, managing, and growing successful e-commerce businesses.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Juaso AI Commerce - Build Smarter E-commerce Stores",
    description: "The world's most intelligent AI-powered platform for launching, managing, and growing successful e-commerce businesses.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="font-sans antialiased">
        {children}
      </body>
    </html>
  );
}
