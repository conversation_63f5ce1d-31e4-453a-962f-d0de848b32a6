import Link from "next/link";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>p, <PERSON>ap, Shield, Globe, Star } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Navigation */}
      <nav className="border-b border-gray-200 bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Juaso AI
                </h1>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <Link href="#features" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                  Features
                </Link>
                <Link href="#pricing" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                  Pricing
                </Link>
                <Link href="/auth/signin" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                  Sign In
                </Link>
                <Link href="/auth/signup" className="btn-primary">
                  Get Started
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Build Smarter
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {" "}E-commerce Stores
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              The world's most intelligent AI-powered platform for launching, managing, and growing successful e-commerce businesses.
              Go beyond simple store generation with strategic AI guidance.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/signup" className="btn-primary text-lg px-8 py-3">
                Start Building Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
              <Link href="#demo" className="btn-secondary text-lg px-8 py-3">
                Watch Demo
              </Link>
            </div>
            <p className="text-sm text-gray-500 mt-4">
              14-day free trial • No credit card required
            </p>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Juaso AI is Better
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We don't just build stores. We create intelligent business partners that help you succeed long-term.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="card text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Brain className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">AI Strategic Intelligence</h3>
              <p className="text-gray-600">
                Deep niche analysis, competitor research, and full brand identity generation for strategic advantage.
              </p>
            </div>

            <div className="card text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">End-to-End Growth</h3>
              <p className="text-gray-600">
                From launch to scale with automated marketing, analytics, and optimization tools.
              </p>
            </div>

            <div className="card text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Globe className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Platform Flexibility</h3>
              <p className="text-gray-600">
                Deploy on Shopify for power users or our built-in platform for simplicity and lower costs.
              </p>
            </div>

            <div className="card text-center">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Zap className="h-6 w-6 text-yellow-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Growth Automation</h3>
              <p className="text-gray-600">
                AI-generated social content, email campaigns, and ad copy to save time and boost sales.
              </p>
            </div>

            <div className="card text-center">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Shield className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Smart Analytics</h3>
              <p className="text-gray-600">
                Natural language insights that tell you exactly what's working and what to do next.
              </p>
            </div>

            <div className="card text-center">
              <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Star className="h-6 w-6 text-indigo-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Premium Quality</h3>
              <p className="text-gray-600">
                Professional-grade stores with premium themes, optimized for conversions and mobile.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Three Phases to Success
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our guided process takes you from idea to profitable business in record time.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">
                1
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">Foundation & Strategy</h3>
              <ul className="text-left space-y-2 text-gray-600">
                <li>• AI Niche & Market Analysis</li>
                <li>• Competitor Research & SWOT</li>
                <li>• Complete Brand Identity Suite</li>
                <li>• Business Names & Logo Generation</li>
                <li>• Mission Statement & USP</li>
              </ul>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-600 text-white rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">
                2
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">Creation & Launch</h3>
              <ul className="text-left space-y-2 text-gray-600">
                <li>• AI Product Curation & Sourcing</li>
                <li>• SEO-Optimized Content Generation</li>
                <li>• Multi-Platform Store Deployment</li>
                <li>• Shopify or Built-in Commerce</li>
                <li>• Mobile-First Premium Themes</li>
              </ul>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-600 text-white rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">
                3
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">Growth & Optimization</h3>
              <ul className="text-left space-y-2 text-gray-600">
                <li>• AI Marketing Co-Pilot</li>
                <li>• Social Media Content Calendar</li>
                <li>• Email Marketing Automation</li>
                <li>• Smart Analytics & Insights</li>
                <li>• SEO & Content Planning</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Start free, scale as you grow. No hidden fees, no surprises.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Free Trial */}
            <div className="card text-center border-2 border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Free Trial</h3>
              <div className="text-3xl font-bold text-gray-900 mb-4">
                $0
                <span className="text-sm font-normal text-gray-600">/14 days</span>
              </div>
              <ul className="text-left space-y-2 text-gray-600 mb-6">
                <li>• Full Phase 1 access</li>
                <li>• Brand identity generation</li>
                <li>• Store preview</li>
                <li>• No store launch</li>
              </ul>
              <Link href="/auth/signup" className="btn-secondary w-full">
                Start Free Trial
              </Link>
            </div>

            {/* Starter */}
            <div className="card text-center border-2 border-blue-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Starter</h3>
              <div className="text-3xl font-bold text-gray-900 mb-4">
                $39
                <span className="text-sm font-normal text-gray-600">/month</span>
              </div>
              <ul className="text-left space-y-2 text-gray-600 mb-6">
                <li>• Phase 1 & 2 features</li>
                <li>• Launch 1 store</li>
                <li>• Basic AI analytics</li>
                <li>• 100 AI credits/month</li>
              </ul>
              <Link href="/auth/signup" className="btn-primary w-full">
                Get Started
              </Link>
            </div>

            {/* Growth */}
            <div className="card text-center border-2 border-green-500 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Most Popular
                </span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Growth</h3>
              <div className="text-3xl font-bold text-gray-900 mb-4">
                $99
                <span className="text-sm font-normal text-gray-600">/month</span>
              </div>
              <ul className="text-left space-y-2 text-gray-600 mb-6">
                <li>• All Starter features</li>
                <li>• Full Phase 3 access</li>
                <li>• AI Marketing Co-Pilot</li>
                <li>• Smart analytics & insights</li>
                <li>• 500 AI credits/month</li>
              </ul>
              <Link href="/auth/signup" className="btn-primary w-full">
                Start Growing
              </Link>
            </div>

            {/* Pro */}
            <div className="card text-center border-2 border-purple-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Pro</h3>
              <div className="text-3xl font-bold text-gray-900 mb-4">
                $249
                <span className="text-sm font-normal text-gray-600">/month</span>
              </div>
              <ul className="text-left space-y-2 text-gray-600 mb-6">
                <li>• All Growth features</li>
                <li>• Manage up to 3 stores</li>
                <li>• Priority support</li>
                <li>• Unlimited AI credits</li>
                <li>• Advanced integrations</li>
              </ul>
              <Link href="/auth/signup" className="btn-primary w-full">
                Go Pro
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Build Your AI-Powered Store?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join thousands of entrepreneurs who are building smarter businesses with Juaso AI Commerce.
          </p>
          <Link href="/auth/signup" className="bg-white text-blue-600 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg text-lg transition-colors duration-200">
            Start Your Free Trial
            <ArrowRight className="ml-2 h-5 w-5 inline" />
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-4">
                Juaso AI
              </h3>
              <p className="text-gray-400">
                The world's most intelligent AI-powered e-commerce platform.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="#features" className="hover:text-white">Features</Link></li>
                <li><Link href="#pricing" className="hover:text-white">Pricing</Link></li>
                <li><Link href="/demo" className="hover:text-white">Demo</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/about" className="hover:text-white">About</Link></li>
                <li><Link href="/contact" className="hover:text-white">Contact</Link></li>
                <li><Link href="/careers" className="hover:text-white">Careers</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/help" className="hover:text-white">Help Center</Link></li>
                <li><Link href="/docs" className="hover:text-white">Documentation</Link></li>
                <li><Link href="/community" className="hover:text-white">Community</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Juaso AI Commerce. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
