import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { openai, AI_MODELS, AI_CREDIT_COSTS } from '@/lib/openai'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const nicheAnalysisSchema = z.object({
  interests: z.array(z.string()).min(1).max(5),
  storeId: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { interests, storeId } = nicheAnalysisSchema.parse(body)

    // Check user's AI credits
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { aiCredits: true }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Create AI prompt for niche analysis
    const prompt = `
    As an e-commerce expert, analyze the following niches for dropshipping potential: ${interests.join(', ')}.
    
    For each niche, provide:
    1. Market size assessment (Small/Medium/Large)
    2. Competition level (Low/Medium/High)
    3. Profitability potential (Low/Medium/High)
    4. Current trends and seasonality
    5. Target audience demographics
    6. Recommended product categories
    7. Overall niche score (1-100)
    
    Format the response as a JSON array with the following structure:
    [
      {
        "niche": "niche name",
        "marketSize": "Large",
        "competition": "Medium",
        "profitability": "High",
        "nicheScore": 85,
        "analysis": "detailed analysis text",
        "trends": ["trend1", "trend2"],
        "targetAudience": "description",
        "recommendedProducts": ["product1", "product2"]
      }
    ]
    `

    const completion = await openai.chat.completions.create({
      model: AI_MODELS.GPT_4_TURBO,
      messages: [
        {
          role: 'system',
          content: 'You are an expert e-commerce analyst specializing in dropshipping market research.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000,
    })

    const analysisText = completion.choices[0]?.message?.content
    if (!analysisText) {
      throw new Error('No analysis generated')
    }

    // Parse the AI response
    let analysisData
    try {
      analysisData = JSON.parse(analysisText)
    } catch (error) {
      // Fallback if JSON parsing fails
      analysisData = [{
        niche: interests[0],
        marketSize: "Medium",
        competition: "Medium",
        profitability: "Medium",
        nicheScore: 70,
        analysis: analysisText,
        trends: [],
        targetAudience: "General consumers",
        recommendedProducts: []
      }]
    }

    // Save analysis to database if storeId is provided
    if (storeId) {
      for (const analysis of analysisData) {
        await prisma.nicheAnalysis.create({
          data: {
            niche: analysis.niche,
            marketSize: analysis.marketSize,
            competition: analysis.competition,
            profitability: analysis.profitability,
            nicheScore: analysis.nicheScore,
            analysis: analysis.analysis,
            trendData: {
              trends: analysis.trends,
              targetAudience: analysis.targetAudience,
              recommendedProducts: analysis.recommendedProducts
            },
            storeId: storeId,
          }
        })
      }
    }

    // Record AI credit usage
    await prisma.aICredit.create({
      data: {
        type: 'ANALYSIS',
        amount: AI_CREDIT_COSTS.NICHE_ANALYSIS,
        description: `Niche analysis for: ${interests.join(', ')}`,
        userId: session.user.id,
      }
    })

    return NextResponse.json({
      success: true,
      data: analysisData
    })

  } catch (error) {
    console.error('Niche analysis error:', error)
    return NextResponse.json(
      { error: 'Failed to analyze niches' },
      { status: 500 }
    )
  }
}
