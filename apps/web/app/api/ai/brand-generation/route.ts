import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { openai, AI_MODELS, AI_CREDIT_COSTS } from '@/lib/openai'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const brandGenerationSchema = z.object({
  niche: z.string().min(1),
  style: z.enum(['modern', 'luxurious', 'playful', 'minimalist', 'bold']),
  targetAudience: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { niche, style, targetAudience } = brandGenerationSchema.parse(body)

    // Create AI prompt for brand generation
    const prompt = `
    Create a comprehensive brand identity for a ${style} e-commerce store in the ${niche} niche.
    ${targetAudience ? `Target audience: ${targetAudience}` : ''}
    
    Generate:
    1. 10-15 unique business names (check for availability)
    2. A compelling mission statement
    3. A unique selling proposition (USP)
    4. Brand voice and tone description
    5. Color palette (primary and secondary colors with hex codes)
    6. Typography recommendations
    7. Target customer persona
    8. Brand values and personality traits
    
    Format the response as JSON:
    {
      "businessNames": ["name1", "name2", ...],
      "missionStatement": "mission text",
      "usp": "unique selling proposition",
      "brandVoice": "voice description",
      "colorPalette": {
        "primary": "#hexcode",
        "secondary": "#hexcode",
        "accent": "#hexcode"
      },
      "typography": {
        "primary": "font name",
        "secondary": "font name"
      },
      "customerPersona": {
        "demographics": "description",
        "psychographics": "description",
        "painPoints": ["point1", "point2"],
        "motivations": ["motivation1", "motivation2"]
      },
      "brandValues": ["value1", "value2", "value3"],
      "personalityTraits": ["trait1", "trait2", "trait3"]
    }
    `

    const completion = await openai.chat.completions.create({
      model: AI_MODELS.GPT_4_TURBO,
      messages: [
        {
          role: 'system',
          content: 'You are a professional brand strategist and designer with expertise in e-commerce branding.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.8,
      max_tokens: 2000,
    })

    const brandText = completion.choices[0]?.message?.content
    if (!brandText) {
      throw new Error('No brand identity generated')
    }

    // Parse the AI response
    let brandData
    try {
      brandData = JSON.parse(brandText)
    } catch (error) {
      // Fallback if JSON parsing fails
      brandData = {
        businessNames: [`${niche.charAt(0).toUpperCase() + niche.slice(1)} Pro`, `Elite ${niche}`, `${niche} Hub`],
        missionStatement: `To provide high-quality ${niche} products that enhance our customers' lives.`,
        usp: `The most trusted source for premium ${niche} products.`,
        brandVoice: `${style} and professional`,
        colorPalette: {
          primary: "#3B82F6",
          secondary: "#64748B",
          accent: "#F59E0B"
        },
        typography: {
          primary: "Inter",
          secondary: "Roboto"
        },
        customerPersona: {
          demographics: "Adults aged 25-45",
          psychographics: "Quality-conscious consumers",
          painPoints: ["Finding reliable products", "High prices"],
          motivations: ["Quality", "Convenience"]
        },
        brandValues: ["Quality", "Trust", "Innovation"],
        personalityTraits: ["Professional", "Reliable", "Modern"]
      }
    }

    // Record AI credit usage
    await prisma.aICredit.create({
      data: {
        type: 'CONTENT_GENERATION',
        amount: AI_CREDIT_COSTS.BRAND_GENERATION,
        description: `Brand generation for ${niche} niche`,
        userId: session.user.id,
      }
    })

    return NextResponse.json({
      success: true,
      data: brandData
    })

  } catch (error) {
    console.error('Brand generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate brand identity' },
      { status: 500 }
    )
  }
}
