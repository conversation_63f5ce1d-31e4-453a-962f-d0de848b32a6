import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createStoreSchema = z.object({
  name: z.string().min(1).max(100),
  niche: z.string().min(1),
  platform: z.enum(['SHOPIFY', 'NEXUS_COMMERCE']),
  description: z.string().optional(),
  brandData: z.object({
    logo: z.string().optional(),
    colorPrimary: z.string().optional(),
    colorSecondary: z.string().optional(),
    fontFamily: z.string().optional(),
    brandVoice: z.string().optional(),
    missionStatement: z.string().optional(),
  }).optional(),
})

// GET /api/stores - Get user's stores
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const stores = await prisma.store.findMany({
      where: {
        userId: session.user.id
      },
      include: {
        products: {
          take: 5,
          orderBy: { createdAt: 'desc' }
        },
        analytics: {
          take: 30,
          orderBy: { date: 'desc' }
        },
        _count: {
          select: {
            products: true,
            orders: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json({
      success: true,
      data: stores
    })

  } catch (error) {
    console.error('Get stores error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch stores' },
      { status: 500 }
    )
  }
}

// POST /api/stores - Create a new store
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { name, niche, platform, description, brandData } = createStoreSchema.parse(body)

    // Generate a unique slug
    const baseSlug = name.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-')
    let slug = baseSlug
    let counter = 1

    while (await prisma.store.findUnique({ where: { slug } })) {
      slug = `${baseSlug}-${counter}`
      counter++
    }

    // Create the store
    const store = await prisma.store.create({
      data: {
        name,
        slug,
        niche,
        platform,
        description,
        userId: session.user.id,
        ...brandData,
        // Generate subdomain for Nexus Commerce
        subdomain: platform === 'NEXUS_COMMERCE' ? slug : undefined,
      },
      include: {
        _count: {
          select: {
            products: true,
            orders: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: store
    })

  } catch (error) {
    console.error('Create store error:', error)
    return NextResponse.json(
      { error: 'Failed to create store' },
      { status: 500 }
    )
  }
}
