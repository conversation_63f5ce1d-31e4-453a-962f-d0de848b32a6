import OpenAI from 'openai'

if (!process.env.OPENAI_API_KEY) {
  throw new Error('Missing OPENAI_API_KEY environment variable')
}

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export const AI_MODELS = {
  GPT_4: 'gpt-4',
  GPT_4_TURBO: 'gpt-4-turbo-preview',
  GPT_3_5_TURBO: 'gpt-3.5-turbo',
  DALL_E_3: 'dall-e-3',
} as const

export const AI_CREDIT_COSTS = {
  CONTENT_GENERATION: 1,
  IMAGE_GENERATION: 5,
  NICHE_ANALYSIS: 3,
  COMPETITOR_ANALYSIS: 2,
  BRAND_GENERATION: 4,
} as const
