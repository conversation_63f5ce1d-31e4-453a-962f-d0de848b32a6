// User and Authentication Types
export interface User {
  id: string
  name?: string
  email: string
  image?: string
  subscriptionTier: 'FREE' | 'STARTER' | 'GROWTH' | 'PRO'
  subscriptionStatus: 'ACTIVE' | 'CANCELLED' | 'EXPIRED'
  subscriptionEndsAt?: Date
  createdAt: Date
  updatedAt: Date
}

// Store Types
export interface Store {
  id: string
  name: string
  slug: string
  description?: string
  niche: string
  status: 'DRAFT' | 'ACTIVE' | 'PAUSED' | 'ARCHIVED'
  platform: 'SHOPIFY' | 'NEXUS_COMMERCE'
  
  // Store URLs and identifiers
  shopifyStoreId?: string
  customDomain?: string
  subdomain?: string
  
  // Brand identity
  logo?: string
  colorPrimary?: string
  colorSecondary?: string
  fontFamily?: string
  brandVoice?: string
  missionStatement?: string
  
  // Analytics
  totalRevenue: number
  totalOrders: number
  conversionRate: number
  
  createdAt: Date
  updatedAt: Date
  userId: string
}

// Product Types
export interface Product {
  id: string
  name: string
  description: string
  price: number
  compareAtPrice?: number
  sku?: string
  
  // Supplier info
  supplierName: string
  supplierProductId: string
  supplierPrice: number
  shippingTime?: string
  
  // SEO and content
  seoTitle?: string
  seoDescription?: string
  tags: string[]
  
  // Product status
  status: 'DRAFT' | 'ACTIVE' | 'ARCHIVED'
  isWinning: boolean
  
  // Analytics
  views: number
  orders: number
  revenue: number
  
  images: string[]
  
  createdAt: Date
  updatedAt: Date
  storeId: string
}

// AI Analysis Types
export interface NicheAnalysis {
  id: string
  niche: string
  marketSize: string
  competition: 'LOW' | 'MEDIUM' | 'HIGH'
  profitability: 'LOW' | 'MEDIUM' | 'HIGH'
  trendData?: {
    trends: string[]
    targetAudience: string
    recommendedProducts: string[]
  }
  nicheScore: number
  analysis: string
  createdAt: Date
  storeId: string
}

export interface CompetitorAnalysis {
  id: string
  competitorName: string
  competitorUrl: string
  strengths: string
  weaknesses: string
  opportunities: string
  threats: string
  createdAt: Date
  storeId: string
}

// Brand Generation Types
export interface BrandIdentity {
  businessNames: string[]
  missionStatement: string
  usp: string
  brandVoice: string
  colorPalette: {
    primary: string
    secondary: string
    accent: string
  }
  typography: {
    primary: string
    secondary: string
  }
  customerPersona: {
    demographics: string
    psychographics: string
    painPoints: string[]
    motivations: string[]
  }
  brandValues: string[]
  personalityTraits: string[]
}

// Analytics Types
export interface Analytics {
  id: string
  date: Date
  visitors: number
  pageViews: number
  orders: number
  revenue: number
  conversionRate: number
  
  // Traffic sources
  organicTraffic: number
  paidTraffic: number
  socialTraffic: number
  directTraffic: number
  
  storeId: string
}

// AI Content Types
export interface AIContent {
  id: string
  type: 'PRODUCT_DESCRIPTION' | 'BLOG_POST' | 'EMAIL' | 'AD_COPY' | 'SOCIAL_MEDIA'
  title?: string
  content: string
  metadata?: Record<string, any>
  createdAt: Date
  storeId: string
}

// Order Types
export interface Order {
  id: string
  orderNumber: string
  email: string
  total: number
  subtotal: number
  tax: number
  shipping: number
  status: 'PENDING' | 'PAID' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED'
  
  // Customer info
  customerName: string
  shippingAddress: Record<string, any>
  billingAddress?: Record<string, any>
  
  createdAt: Date
  updatedAt: Date
  storeId: string
}

// API Response Types
export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Form Types
export interface CreateStoreForm {
  name: string
  niche: string
  platform: 'SHOPIFY' | 'NEXUS_COMMERCE'
  description?: string
}

export interface NicheAnalysisForm {
  interests: string[]
  storeId?: string
}

export interface BrandGenerationForm {
  niche: string
  style: 'modern' | 'luxurious' | 'playful' | 'minimalist' | 'bold'
  targetAudience?: string
}

// Subscription Types
export interface SubscriptionPlan {
  id: string
  name: string
  price: number
  interval: 'month' | 'year'
  features: string[]
  aiCredits: number
  maxStores: number
  popular?: boolean
}

// Dashboard Types
export interface DashboardStats {
  totalStores: number
  totalRevenue: number
  totalOrders: number
  averageConversionRate: number
  recentActivity: ActivityItem[]
}

export interface ActivityItem {
  id: string
  type: 'store_created' | 'product_added' | 'order_received' | 'analysis_completed'
  title: string
  description: string
  timestamp: Date
  storeId?: string
}
