{"name": "@repo/ui", "version": "0.0.0", "private": true, "exports": {"./*": "./src/*.tsx"}, "scripts": {"lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component", "check-types": "tsc --noEmit"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.31.0", "typescript": "5.8.2"}, "dependencies": {"clsx": "^2.1.1", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1"}}